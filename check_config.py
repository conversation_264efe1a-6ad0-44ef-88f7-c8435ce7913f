#!/usr/bin/env python3
"""
Configuration checker for OpenAI Speech-to-Speech application
"""

import os
from dotenv import load_dotenv

def main():
    print("🔧 Configuration Check")
    print("=" * 30)
    
    # Load environment variables
    load_dotenv()
    
    # Check OpenAI API key
    api_key = os.getenv("OPENAI_API_KEY")
    if api_key:
        print(f"✅ OpenAI API Key: {api_key[:8]}...{api_key[-4:]}")
    else:
        print("❌ OpenAI API Key: Not found")
        print("💡 Set OPENAI_API_KEY in .env file or environment")
    
    # Check .env file
    env_file = ".env"
    if os.path.exists(env_file):
        print(f"✅ .env file: Found")
    else:
        print(f"❌ .env file: Not found")
        print(f"💡 Copy .env.example to .env and add your API key")
    
    # Check required files
    required_files = [
        "app.py",
        "config.py", 
        "audio_processor.py",
        "websocket_client.py",
        "requirements.txt"
    ]
    
    print("\n📁 Required Files:")
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file}")
    
    print("\n🌐 Next Steps:")
    if not api_key:
        print("1. Get OpenAI API key from: https://platform.openai.com/api-keys")
        print("2. Copy .env.example to .env")
        print("3. Add your API key to .env file")
        print("4. Run: python run_demo.py")
    else:
        print("✅ Configuration looks good!")
        print("🚀 Run: python run_demo.py")

if __name__ == "__main__":
    main()
