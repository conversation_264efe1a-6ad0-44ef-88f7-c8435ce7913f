import asyncio
import json
import base64
import websockets
import logging
from typing import Callable, Optional
from config import Config

logger = logging.getLogger(__name__)

class OpenAIRealtimeClient:
    def __init__(self, on_audio_response: Callable[[bytes], None], on_conversation_event: Optional[Callable[[str, dict], None]] = None):
        self.websocket = None
        self.on_audio_response = on_audio_response
        self.on_conversation_event = on_conversation_event
        self.is_connected = False
        self.session_id = None
        self.is_ai_speaking = False
        self.current_response_id = None
        self.conversation_state = "listening"  # "listening", "speaking", "processing"

    async def connect(self):
        """Connect to OpenAI Realtime API"""
        try:
            # Create headers for the WebSocket connection
            headers = [
                ("Authorization", f"Bearer {Config.OPENAI_API_KEY}"),
                ("OpenAI-Beta", "realtime=v1")
            ]

            self.websocket = await websockets.connect(
                Config.OPENAI_REALTIME_URL,
                additional_headers=headers
            )
            self.is_connected = True
            logger.info("Connected to OpenAI Realtime API")

            # Start session
            await self._start_session()

            # Start listening for responses
            asyncio.create_task(self._listen_for_responses())

        except Exception as e:
            logger.error(f"Failed to connect to OpenAI Realtime API: {e}")
            self.is_connected = False
            raise

    async def _start_session(self):
        """Initialize the session with OpenAI"""
        session_config = {
            "type": "session.update",
            "session": {
                "modalities": ["text", "audio"],
                "instructions": "You are a helpful voice assistant. Respond naturally and conversationally. Keep responses concise and engaging. You can be interrupted at any time.",
                "voice": "alloy",
                "input_audio_format": "pcm16",
                "output_audio_format": "pcm16",
                "input_audio_transcription": {
                    "model": "whisper-1"
                },
                "turn_detection": {
                    "type": "server_vad",
                    "threshold": Config.VAD_THRESHOLD,
                    "prefix_padding_ms": Config.VAD_PREFIX_PADDING_MS,
                    "silence_duration_ms": Config.VAD_SILENCE_DURATION_MS
                }
            }
        }

        await self.websocket.send(json.dumps(session_config))
        logger.info("Session configuration sent")

    async def send_audio(self, audio_data: bytes):
        """Send audio data to OpenAI"""
        if not self.is_connected or not self.websocket:
            logger.warning("Not connected to OpenAI Realtime API")
            return

        try:
            # Convert audio to base64
            audio_base64 = base64.b64encode(audio_data).decode('utf-8')

            message = {
                "type": "input_audio_buffer.append",
                "audio": audio_base64
            }

            await self.websocket.send(json.dumps(message))

        except Exception as e:
            logger.error(f"Error sending audio: {e}")

    async def commit_audio(self):
        """Commit the audio buffer and trigger response generation"""
        if not self.is_connected or not self.websocket:
            return

        try:
            # If AI is currently speaking and interruption is enabled, cancel current response
            if self.is_ai_speaking and Config.INTERRUPTION_ENABLED:
                await self.cancel_response()

            message = {
                "type": "input_audio_buffer.commit"
            }
            await self.websocket.send(json.dumps(message))

            # Create response
            response_message = {
                "type": "response.create",
                "response": {
                    "modalities": ["audio"],
                    "instructions": "Please respond to the user's input."
                }
            }
            await self.websocket.send(json.dumps(response_message))

            self.conversation_state = "processing"
            self._notify_conversation_event("processing", {"message": "Processing user input"})

        except Exception as e:
            logger.error(f"Error committing audio: {e}")

    async def cancel_response(self):
        """Cancel the current AI response (for interruption)"""
        if not self.is_connected or not self.websocket or not self.current_response_id:
            return

        try:
            cancel_message = {
                "type": "response.cancel"
            }
            await self.websocket.send(json.dumps(cancel_message))

            self.is_ai_speaking = False
            self.current_response_id = None
            self.conversation_state = "listening"
            self._notify_conversation_event("interrupted", {"message": "AI response interrupted"})
            logger.info("AI response cancelled due to interruption")

        except Exception as e:
            logger.error(f"Error cancelling response: {e}")

    def _notify_conversation_event(self, event_type: str, data: dict):
        """Notify about conversation events"""
        if self.on_conversation_event:
            self.on_conversation_event(event_type, data)

    async def _listen_for_responses(self):
        """Listen for responses from OpenAI"""
        try:
            async for message in self.websocket:
                data = json.loads(message)
                await self._handle_message(data)

        except websockets.exceptions.ConnectionClosed:
            logger.info("WebSocket connection closed")
            self.is_connected = False
        except Exception as e:
            logger.error(f"Error listening for responses: {e}")
            self.is_connected = False

    async def _handle_message(self, data: dict):
        """Handle incoming messages from OpenAI"""
        message_type = data.get("type")

        if message_type == "session.created":
            self.session_id = data.get("session", {}).get("id")
            logger.info(f"Session created: {self.session_id}")
            self._notify_conversation_event("session_created", {"session_id": self.session_id})

        elif message_type == "input_audio_buffer.speech_started":
            self.conversation_state = "speaking"
            self._notify_conversation_event("user_speaking", {"message": "User started speaking"})
            logger.info("User speech detected")

        elif message_type == "input_audio_buffer.speech_stopped":
            self.conversation_state = "processing"
            self._notify_conversation_event("user_stopped", {"message": "User stopped speaking"})
            logger.info("User speech ended")

        elif message_type == "response.created":
            self.current_response_id = data.get("response", {}).get("id")
            logger.info(f"Response created: {self.current_response_id}")

        elif message_type == "response.audio.delta":
            # Audio response chunk
            if not self.is_ai_speaking:
                self.is_ai_speaking = True
                self.conversation_state = "ai_speaking"
                self._notify_conversation_event("ai_speaking", {"message": "AI started responding"})

            audio_base64 = data.get("delta", "")
            if audio_base64:
                audio_data = base64.b64decode(audio_base64)
                if self.on_audio_response:
                    self.on_audio_response(audio_data)

        elif message_type == "response.audio.done":
            self.is_ai_speaking = False
            self.conversation_state = "listening"
            self.current_response_id = None
            self._notify_conversation_event("ai_finished", {"message": "AI finished responding"})
            logger.info("Audio response completed")

        elif message_type == "response.done":
            self.is_ai_speaking = False
            self.conversation_state = "listening"
            self.current_response_id = None
            self._notify_conversation_event("response_complete", {"message": "Response completed"})
            logger.info("Response completed")

        elif message_type == "error":
            error_info = data.get("error", {})
            self._notify_conversation_event("error", {"error": error_info})
            logger.error(f"OpenAI API error: {error_info}")

        else:
            # Log other message types for debugging
            logger.debug(f"Received message type: {message_type}")

    async def disconnect(self):
        """Disconnect from OpenAI Realtime API"""
        self.is_connected = False
        if self.websocket:
            await self.websocket.close()
            logger.info("Disconnected from OpenAI Realtime API")
