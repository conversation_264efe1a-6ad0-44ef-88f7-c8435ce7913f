# OpenAI Speech-to-Speech with Streamlit WebRTC

A real-time voice conversation application using Streamlit WebRTC and OpenAI's Realtime API for speech-to-speech interaction.

## Features

- 🎤 Real-time microphone input capture using WebRTC
- 🔊 Live audio streaming to OpenAI's Realtime API
- 🎧 Real-time audio response playback
- 🌐 WebSocket-based communication with OpenAI
- ⚙️ Configurable audio settings
- 🛡️ Error handling and connection management

## Prerequisites

- Python 3.8 or higher
- OpenAI API key with access to the Realtime API
- Microphone and speakers/headphones
- Modern web browser with WebRTC support

## Installation

1. **Clone or download this repository**

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables:**
   Create a `.env` file in the project root:
   ```
   OPENAI_API_KEY=your_openai_api_key_here
   ```

## Usage

1. **Start the application:**
   ```bash
   streamlit run app.py
   ```

2. **Open your browser** and navigate to the displayed URL (usually `http://localhost:8501`)

3. **Configure the application:**
   - Enter your OpenAI API key in the sidebar (if not set in .env)
   - Review audio settings

4. **Start recording:**
   - Click "Start Recording"
   - Allow microphone access when prompted
   - Speak into your microphone
   - Listen for AI responses through your speakers

## Configuration

### Audio Settings
- **Sample Rate:** 24kHz (required by OpenAI Realtime API)
- **Channels:** Mono (1 channel)
- **Format:** 16-bit PCM
- **Chunk Size:** 1024 samples

### OpenAI Settings
- **Model:** gpt-4o-realtime-preview-2024-10-01
- **Voice:** alloy (configurable in websocket_client.py)
- **Turn Detection:** Server-side Voice Activity Detection (VAD)

## Architecture

### Components

1. **app.py** - Main Streamlit application with UI
2. **audio_processor.py** - Audio processing and buffering
3. **websocket_client.py** - WebSocket client for OpenAI Realtime API
4. **config.py** - Configuration settings
5. **requirements.txt** - Python dependencies

### Data Flow

1. Microphone → WebRTC → Streamlit
2. Audio preprocessing and buffering
3. WebSocket transmission to OpenAI Realtime API
4. Real-time audio response streaming
5. Audio playback through browser

## Troubleshooting

### Common Issues

1. **"OPENAI_API_KEY environment variable is required"**
   - Set your API key in the .env file or sidebar

2. **WebRTC connection issues**
   - Ensure microphone permissions are granted
   - Try refreshing the page
   - Check browser compatibility

3. **Audio quality issues**
   - Check microphone settings
   - Ensure stable internet connection
   - Verify audio device configuration

4. **OpenAI API errors**
   - Verify API key validity
   - Check API quota and billing
   - Ensure Realtime API access

### Browser Compatibility

- ✅ Chrome/Chromium (recommended)
- ✅ Firefox
- ✅ Safari (macOS)
- ❌ Internet Explorer (not supported)

## Development

### Running in Development Mode

```bash
streamlit run app.py --server.runOnSave true
```

### Logging

The application uses Python's logging module. To see debug logs:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## API Costs

The OpenAI Realtime API has usage-based pricing:
- Input audio: $0.06 per minute
- Output audio: $0.24 per minute

Monitor your usage in the OpenAI dashboard.

## Security Notes

- Never commit API keys to version control
- Use environment variables for sensitive configuration
- Consider implementing rate limiting for production use

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source. Please check the license file for details.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review OpenAI Realtime API documentation
3. Open an issue in the repository
