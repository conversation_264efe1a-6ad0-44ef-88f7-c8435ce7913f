#!/usr/bin/env python3
"""
Demo runner for the OpenAI Speech-to-Speech application
"""

import os
import sys
import subprocess

def check_api_key():
    """Check if OpenAI API key is available"""
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ OpenAI API key not found!")
        print("\n📝 To set up your API key:")
        print("1. Copy .env.example to .env:")
        print("   cp .env.example .env")
        print("\n2. Edit .env and add your OpenAI API key:")
        print("   OPENAI_API_KEY=your_actual_api_key_here")
        print("\n3. Or set it as an environment variable:")
        print("   export OPENAI_API_KEY=your_actual_api_key_here")
        print("\n🔗 Get your API key from: https://platform.openai.com/api-keys")
        return False
    
    print(f"✅ OpenAI API key found: {api_key[:8]}...")
    return True

def run_streamlit():
    """Run the Streamlit application"""
    try:
        print("🚀 Starting Streamlit application...")
        print("📱 The app will open in your browser automatically")
        print("🛑 Press Ctrl+C to stop the application")
        print("-" * 50)
        
        # Run streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.headless", "false",
            "--server.runOnSave", "true",
            "--browser.gatherUsageStats", "false"
        ])
        
    except KeyboardInterrupt:
        print("\n🛑 Application stopped by user")
    except Exception as e:
        print(f"❌ Error running application: {e}")

def main():
    print("🎤 OpenAI Speech-to-Speech Demo")
    print("=" * 40)
    
    # Check API key
    if not check_api_key():
        return 1
    
    # Run the application
    run_streamlit()
    return 0

if __name__ == "__main__":
    sys.exit(main())
