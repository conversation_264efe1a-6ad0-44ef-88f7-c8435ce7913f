import os
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging for better monitoring
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class Config:
    # OpenAI Configuration
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    OPENAI_REALTIME_URL = "wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-10-01"

    # Audio Configuration
    SAMPLE_RATE = 24000  # OpenAI Realtime API expects 24kHz
    CHANNELS = 1  # Mono audio
    CHUNK_SIZE = 1024  # Audio chunk size for processing
    AUDIO_FORMAT = "pcm16"  # 16-bit PCM format

    # WebRTC Configuration
    RTC_CONFIGURATION = {
        "iceServers": [{"urls": ["stun:stun.l.google.com:19302"]}]
    }

    # Application Settings
    MAX_RECORDING_DURATION = 300  # 5 minutes max
    SILENCE_THRESHOLD = 0.01  # Threshold for detecting silence
    SILENCE_DURATION = 2.0  # Seconds of silence before stopping

    # Enhanced VAD Settings (ChatGPT-like conversation)
    VAD_THRESHOLD = 0.3  # Voice activity detection threshold (0.0-1.0)
    VAD_SILENCE_DURATION_MS = 700  # Milliseconds of silence before turn ends
    VAD_PREFIX_PADDING_MS = 300  # Audio to include before speech starts
    INTERRUPTION_ENABLED = True  # Allow interrupting AI responses
    CONVERSATION_MODE = "continuous"  # "push_to_talk" or "continuous"

    # Audio Response Settings
    ENABLE_AUDIO_PLAYBACK = True  # Play AI responses through speakers
    RESPONSE_BUFFER_SIZE = 4096  # Buffer size for audio playback

    @classmethod
    def validate(cls):
        """Validate required configuration"""
        if not cls.OPENAI_API_KEY:
            raise ValueError("OPENAI_API_KEY environment variable is required")
        return True
