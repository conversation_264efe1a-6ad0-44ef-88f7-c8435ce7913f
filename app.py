import streamlit as st
import numpy as np
import logging
import queue
import threading
import time
from streamlit_webrtc import webrtc_streamer, WebRtcMode, RTCConfiguration
import av
from config import Config
from audio_processor import AudioProcessor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="OpenAI Speech-to-Speech with WebRTC",
    page_icon="🎤",
    layout="wide"
)

# Initialize session state
if "audio_processor" not in st.session_state:
    st.session_state.audio_processor = None
if "is_recording" not in st.session_state:
    st.session_state.is_recording = False
if "audio_output_queue" not in st.session_state:
    st.session_state.audio_output_queue = queue.Queue()
if "conversation_state" not in st.session_state:
    st.session_state.conversation_state = "idle"
if "conversation_events" not in st.session_state:
    st.session_state.conversation_events = []
if "conversation_mode" not in st.session_state:
    st.session_state.conversation_mode = Config.CONVERSATION_MODE

# Global variables to handle WebRTC callback (since it runs in separate thread)
global_audio_processor = None
global_is_recording = False

def audio_frame_callback(frame: av.AudioFrame) -> av.AudioFrame:
    """Process incoming audio frames"""
    global global_audio_processor, global_is_recording

    if global_audio_processor and global_is_recording:
        # Convert audio frame to numpy array
        audio_array = frame.to_ndarray()

        # Set sample rate for processor
        global_audio_processor.set_sample_rate(frame.sample_rate)

        # Add audio to processor
        global_audio_processor.add_audio_chunk(audio_array)

    return frame

def handle_audio_output(audio_data: bytes):
    """Handle audio output from OpenAI"""
    try:
        st.session_state.audio_output_queue.put(audio_data, block=False)
    except queue.Full:
        logger.warning("Audio output queue is full")

def handle_conversation_event(event_type: str, data: dict):
    """Handle conversation events"""
    try:
        # Update conversation state
        if event_type == "user_speaking":
            st.session_state.conversation_state = "🎤 You're speaking..."
        elif event_type == "user_stopped":
            st.session_state.conversation_state = "🤔 Processing..."
        elif event_type == "ai_speaking":
            st.session_state.conversation_state = "🤖 AI is responding..."
        elif event_type in ["ai_finished", "response_complete"]:
            st.session_state.conversation_state = "👂 Listening..."
        elif event_type == "interrupted":
            st.session_state.conversation_state = "⚡ Interrupted - Listening..."
        elif event_type == "error":
            st.session_state.conversation_state = "❌ Error occurred"

        # Add to events log
        event_entry = {
            "type": event_type,
            "message": data.get("message", ""),
            "timestamp": time.time()
        }
        st.session_state.conversation_events.append(event_entry)

        # Keep only last 10 events
        if len(st.session_state.conversation_events) > 10:
            st.session_state.conversation_events = st.session_state.conversation_events[-10:]

        logger.info(f"Conversation event: {event_type} - {data.get('message', '')}")

    except Exception as e:
        logger.error(f"Error handling conversation event: {e}")

def start_recording():
    """Start audio recording and processing"""
    global global_audio_processor, global_is_recording

    try:
        Config.validate()

        if not st.session_state.audio_processor:
            st.session_state.audio_processor = AudioProcessor()

        # Start processing with conversation event handling
        st.session_state.audio_processor.start_processing(
            on_audio_output=handle_audio_output,
            on_conversation_event=handle_conversation_event
        )
        st.session_state.is_recording = True
        st.session_state.conversation_state = "👂 Listening..."

        # Update global variables for WebRTC callback
        global_audio_processor = st.session_state.audio_processor
        global_is_recording = True

        if st.session_state.conversation_mode == "continuous":
            st.success("🎤 Continuous conversation started! Just speak naturally - I can be interrupted anytime.")
        else:
            st.success("🎤 Push-to-talk mode! Speak into your microphone.")

    except ValueError as e:
        st.error(f"Configuration error: {e}")
    except Exception as e:
        st.error(f"Failed to start recording: {e}")
        logger.error(f"Error starting recording: {e}")

def stop_recording():
    """Stop audio recording and processing"""
    global global_audio_processor, global_is_recording

    if st.session_state.audio_processor:
        st.session_state.audio_processor.stop_processing()

    st.session_state.is_recording = False
    st.session_state.conversation_state = "idle"

    # Update global variables
    global_is_recording = False
    global_audio_processor = None

    st.info("🛑 Conversation ended.")

def main():
    st.title("🎤 OpenAI Speech-to-Speech with WebRTC")
    st.markdown("**ChatGPT-like voice conversation** with interruption support and real-time responses")

    # Conversation state display
    if st.session_state.conversation_state != "idle":
        st.info(f"**Status:** {st.session_state.conversation_state}")

    # Sidebar configuration
    with st.sidebar:
        st.header("⚙️ Configuration")

        # API Key input
        api_key = st.text_input(
            "OpenAI API Key",
            type="password",
            value=Config.OPENAI_API_KEY or "",
            help="Enter your OpenAI API key"
        )

        if api_key:
            Config.OPENAI_API_KEY = api_key

        st.markdown("---")

        # Conversation Mode
        st.subheader("💬 Conversation Mode")
        conversation_mode = st.selectbox(
            "Mode",
            ["continuous", "push_to_talk"],
            index=0 if st.session_state.conversation_mode == "continuous" else 1,
            help="Continuous: Natural conversation with interruption support\nPush-to-talk: Manual control"
        )
        st.session_state.conversation_mode = conversation_mode

        # VAD Settings
        st.subheader("🎙️ Voice Detection")
        vad_threshold = st.slider(
            "VAD Sensitivity",
            min_value=0.1,
            max_value=1.0,
            value=Config.VAD_THRESHOLD,
            step=0.1,
            help="Higher = less sensitive to background noise"
        )
        Config.VAD_THRESHOLD = vad_threshold

        interruption_enabled = st.checkbox(
            "Enable Interruption",
            value=Config.INTERRUPTION_ENABLED,
            help="Allow interrupting AI responses"
        )
        Config.INTERRUPTION_ENABLED = interruption_enabled

        st.markdown("---")

        # Audio settings
        st.subheader("🔊 Audio Settings")
        st.info(f"Sample Rate: {Config.SAMPLE_RATE} Hz")
        st.info(f"Channels: {Config.CHANNELS} (Mono)")
        st.info(f"Format: {Config.AUDIO_FORMAT}")

        st.markdown("---")

        # Connection status
        st.subheader("📡 Status")
        if st.session_state.is_recording:
            st.success("🟢 Conversation Active")

            # Show conversation events
            if st.session_state.conversation_events:
                st.subheader("📝 Recent Events")
                for event in st.session_state.conversation_events[-3:]:
                    event_time = time.strftime("%H:%M:%S", time.localtime(event["timestamp"]))
                    st.text(f"{event_time}: {event['type']}")
        else:
            st.info("🔴 Conversation Inactive")

    # Main content area
    col1, col2 = st.columns([2, 1])

    with col1:
        st.subheader("🎙️ Audio Input")

        # WebRTC streamer for audio input
        webrtc_ctx = webrtc_streamer(
            key="audio-input",
            mode=WebRtcMode.SENDONLY,
            audio_frame_callback=audio_frame_callback,
            rtc_configuration=RTCConfiguration(Config.RTC_CONFIGURATION),
            media_stream_constraints={
                "video": False,
                "audio": {
                    "sampleRate": Config.SAMPLE_RATE,
                    "channelCount": Config.CHANNELS,
                    "echoCancellation": True,
                    "noiseSuppression": True,
                    "autoGainControl": True,
                }
            },
            async_processing=True,
        )

        # Control buttons
        col_start, col_stop = st.columns(2)

        with col_start:
            if st.button("🎤 Start Conversation", disabled=st.session_state.is_recording):
                start_recording()

        with col_stop:
            if st.button("🛑 End Conversation", disabled=not st.session_state.is_recording):
                stop_recording()

        # Instructions based on mode
        if not st.session_state.is_recording:
            if st.session_state.conversation_mode == "continuous":
                st.info("""
                **🔄 Continuous Conversation Mode:**
                1. Click "Start Conversation"
                2. Allow microphone access
                3. **Just speak naturally** - the AI will respond automatically
                4. **You can interrupt** the AI anytime by speaking
                5. The conversation flows like ChatGPT mobile app
                """)
            else:
                st.info("""
                **🎯 Push-to-Talk Mode:**
                1. Click "Start Conversation"
                2. Allow microphone access
                3. Speak when you want to talk
                4. Wait for AI response
                5. Repeat as needed
                """)
        else:
            # Live conversation tips
            if st.session_state.conversation_mode == "continuous":
                st.success("""
                **💡 Conversation Tips:**
                - Speak naturally, pauses are okay
                - Interrupt the AI anytime by speaking
                - The AI will wait for you to finish
                - Say "goodbye" or click stop to end
                """)
            else:
                st.success("""
                **💡 Push-to-Talk Tips:**
                - Speak clearly into your microphone
                - Wait for the AI to finish responding
                - Click "End Conversation" when done
                """)

    with col2:
        st.subheader("🔊 Audio Output")

        # Audio output status
        if st.session_state.is_recording:
            st.info("🎧 Audio responses will play through your speakers")

            # Display audio output queue status
            queue_size = st.session_state.audio_output_queue.qsize()
            if queue_size > 0:
                st.success(f"📦 {queue_size} audio chunks in output queue")
        else:
            st.info("Start recording to receive audio responses")

    # Footer
    st.markdown("---")
    st.markdown("""
    **🎯 Enhanced Features:**
    - **Real-time Voice Activity Detection (VAD)** with configurable sensitivity
    - **Interruption Support** - interrupt the AI anytime like ChatGPT mobile
    - **Continuous Conversation Mode** - natural back-and-forth dialogue
    - **Smart Turn Detection** - automatically detects when you stop speaking
    - **Low Latency** - optimized for real-time conversation

    **💰 API Usage:** OpenAI Realtime API charges ~$0.06/min input + $0.24/min output
    """)

    # Technical details in expander
    with st.expander("🔧 Technical Details"):
        st.markdown(f"""
        **Current Configuration:**
        - VAD Threshold: {Config.VAD_THRESHOLD}
        - Silence Duration: {Config.VAD_SILENCE_DURATION_MS}ms
        - Interruption: {'Enabled' if Config.INTERRUPTION_ENABLED else 'Disabled'}
        - Mode: {st.session_state.conversation_mode.title()}
        - Sample Rate: {Config.SAMPLE_RATE}Hz
        - Audio Format: {Config.AUDIO_FORMAT}
        """)

if __name__ == "__main__":
    main()
