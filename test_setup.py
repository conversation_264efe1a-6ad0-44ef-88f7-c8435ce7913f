#!/usr/bin/env python3
"""
Test script to verify the setup and dependencies
"""

import sys
import importlib
import os

def test_imports():
    """Test if all required packages can be imported"""
    required_packages = [
        'streamlit',
        'streamlit_webrtc',
        'numpy',
        'scipy',
        'websockets',
        'openai',
        'dotenv',
        'av',
    ]
    
    print("Testing package imports...")
    failed_imports = []
    
    for package in required_packages:
        try:
            importlib.import_module(package)
            print(f"✅ {package}")
        except ImportError as e:
            print(f"❌ {package}: {e}")
            failed_imports.append(package)
    
    return failed_imports

def test_config():
    """Test configuration"""
    print("\nTesting configuration...")
    
    try:
        from config import Config
        Config.validate()
        print("✅ Configuration valid")
        return True
    except ValueError as e:
        print(f"❌ Configuration error: {e}")
        print("💡 Make sure to set OPENAI_API_KEY in .env file")
        return False
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_audio_processor():
    """Test audio processor initialization"""
    print("\nTesting audio processor...")
    
    try:
        from audio_processor import AudioProcessor
        processor = AudioProcessor()
        print("✅ AudioProcessor can be initialized")
        return True
    except Exception as e:
        print(f"❌ AudioProcessor error: {e}")
        return False

def test_websocket_client():
    """Test WebSocket client initialization"""
    print("\nTesting WebSocket client...")
    
    try:
        from websocket_client import OpenAIRealtimeClient
        
        def dummy_callback(audio_data):
            pass
        
        client = OpenAIRealtimeClient(dummy_callback)
        print("✅ OpenAIRealtimeClient can be initialized")
        return True
    except Exception as e:
        print(f"❌ OpenAIRealtimeClient error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing OpenAI Speech-to-Speech Setup\n")
    
    # Test imports
    failed_imports = test_imports()
    
    if failed_imports:
        print(f"\n❌ Failed to import: {', '.join(failed_imports)}")
        print("💡 Run: pip install -r requirements.txt")
        return False
    
    # Test configuration
    config_ok = test_config()
    
    # Test components
    audio_ok = test_audio_processor()
    websocket_ok = test_websocket_client()
    
    # Summary
    print("\n" + "="*50)
    if config_ok and audio_ok and websocket_ok:
        print("✅ All tests passed! You can run the application with:")
        print("   streamlit run app.py")
    else:
        print("❌ Some tests failed. Please fix the issues above.")
    
    return config_ok and audio_ok and websocket_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
