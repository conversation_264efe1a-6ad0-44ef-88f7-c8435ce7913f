# 🎤 OpenAI Speech-to-Speech Setup Guide

## ✅ What's Been Created

Your Streamlit WebRTC application for OpenAI Speech-to-Speech is now ready! Here's what has been built:

### 📁 Project Structure
```
test-openai-stream-streamlit-webrtc/
├── app.py                 # Main Streamlit application
├── config.py             # Configuration settings
├── audio_processor.py    # Audio processing logic
├── websocket_client.py   # OpenAI Realtime API client
├── requirements.txt      # Python dependencies
├── .env.example         # Environment template
├── README.md            # Detailed documentation
├── test_setup.py        # Setup verification script
├── check_config.py      # Configuration checker
├── run_demo.py          # Demo runner script
└── SETUP_GUIDE.md       # This guide
```

### 🚀 Features Implemented
- ✅ Real-time microphone input via WebRTC
- ✅ WebSocket connection to OpenAI Realtime API
- ✅ Audio preprocessing (24kHz, 16-bit PCM, mono)
- ✅ Real-time speech-to-speech processing
- ✅ Audio output playback
- ✅ User-friendly Streamlit interface
- ✅ Error handling and connection management
- ✅ Configurable settings

## 🔧 Quick Setup

### 1. Install Dependencies (Already Done ✅)
```bash
pip install -r requirements.txt
```

### 2. Configure OpenAI API Key
```bash
# Copy the environment template
cp .env.example .env

# Edit .env and add your API key
# OPENAI_API_KEY=your_actual_api_key_here
```

### 3. Verify Setup
```bash
python check_config.py
```

### 4. Run the Application
```bash
python run_demo.py
# OR
streamlit run app.py
```

## 🎯 How to Use

1. **Open the application** - It should open automatically in your browser
2. **Enter API Key** - Add your OpenAI API key in the sidebar
3. **Start Recording** - Click "Start Recording" button
4. **Allow Microphone** - Grant microphone permissions when prompted
5. **Speak** - Talk into your microphone
6. **Listen** - Hear AI responses through your speakers

## 🔑 Getting OpenAI API Key

1. Visit [OpenAI Platform](https://platform.openai.com/api-keys)
2. Sign in to your account
3. Create a new API key
4. Copy the key and add it to your `.env` file

**Note:** You need access to the OpenAI Realtime API (currently in beta).

## 🛠️ Technical Details

### Audio Configuration
- **Sample Rate:** 24kHz (required by OpenAI)
- **Format:** 16-bit PCM
- **Channels:** Mono (1 channel)
- **Chunk Size:** 1024 samples

### OpenAI Settings
- **Model:** gpt-4o-realtime-preview-2024-10-01
- **Voice:** alloy
- **Turn Detection:** Server-side VAD
- **Connection:** WebSocket

### Browser Requirements
- ✅ Chrome/Chromium (recommended)
- ✅ Firefox
- ✅ Safari (macOS)
- ❌ Internet Explorer (not supported)

## 🧪 Testing

Run the test suite to verify everything is working:
```bash
python test_setup.py
```

## 🔍 Troubleshooting

### Common Issues

1. **"OPENAI_API_KEY environment variable is required"**
   - Solution: Set your API key in `.env` file

2. **Microphone not working**
   - Solution: Check browser permissions and refresh page

3. **No audio output**
   - Solution: Check speaker settings and browser audio permissions

4. **WebRTC connection failed**
   - Solution: Try refreshing the page or use Chrome browser

### Debug Mode
To see detailed logs, edit `config.py` and add:
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 💰 Cost Considerations

OpenAI Realtime API pricing:
- Input audio: $0.06 per minute
- Output audio: $0.24 per minute

Monitor usage in your OpenAI dashboard.

## 🎉 You're Ready!

The application is now running and ready to use! The Streamlit interface should be open in your browser at http://localhost:80.

### Next Steps:
1. Add your OpenAI API key
2. Test the microphone input
3. Enjoy real-time voice conversations with AI!

For detailed documentation, see `README.md`.
