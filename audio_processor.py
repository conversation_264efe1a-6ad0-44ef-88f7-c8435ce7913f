import numpy as np
import asyncio
import threading
import queue
import logging
from typing import Optional, Callable
from scipy import signal
import time
from config import Config
from websocket_client import OpenAIRealtimeClient

logger = logging.getLogger(__name__)

class AudioProcessor:
    def __init__(self):
        self.audio_queue = queue.Queue()
        self.output_queue = queue.Queue()
        self.is_processing = False
        self.openai_client = None
        self.processing_thread = None
        self.silence_start_time = None
        self.last_audio_time = None

        # Enhanced conversation state
        self.conversation_state = "idle"  # "idle", "listening", "speaking", "ai_responding"
        self.is_user_speaking = False
        self.is_ai_speaking = False
        self.conversation_events = []
        self.on_conversation_event = None

        # Audio playback
        self.audio_output_buffer = queue.Queue()
        self.playback_thread = None

    def start_processing(self, on_audio_output: Callable[[bytes], None], on_conversation_event: Optional[Callable[[str, dict], None]] = None):
        """Start audio processing with OpenAI integration"""
        if self.is_processing:
            return

        self.is_processing = True
        self.on_audio_output = on_audio_output
        self.on_conversation_event = on_conversation_event

        # Initialize OpenAI client with conversation event handling
        self.openai_client = OpenAIRealtimeClient(
            on_audio_response=self._handle_audio_response,
            on_conversation_event=self._handle_conversation_event
        )

        # Start processing thread
        self.processing_thread = threading.Thread(target=self._process_audio_loop)
        self.processing_thread.daemon = True
        self.processing_thread.start()

        # Start audio playback thread if enabled
        if Config.ENABLE_AUDIO_PLAYBACK:
            self.playback_thread = threading.Thread(target=self._audio_playback_loop)
            self.playback_thread.daemon = True
            self.playback_thread.start()

        self.conversation_state = "listening"
        self._notify_conversation_event("started", {"message": "Audio processing started"})
        logger.info("Audio processing started")

    def stop_processing(self):
        """Stop audio processing"""
        self.is_processing = False

        if self.processing_thread:
            self.processing_thread.join(timeout=5.0)

        if self.openai_client:
            try:
                # Try to disconnect gracefully
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                loop.run_until_complete(self.openai_client.disconnect())
                loop.close()
            except Exception as e:
                logger.warning(f"Error during disconnect: {e}")

        logger.info("Audio processing stopped")

    def add_audio_chunk(self, audio_data: np.ndarray):
        """Add audio chunk to processing queue"""
        if not self.is_processing:
            return

        try:
            # Convert to the right format for OpenAI (16-bit PCM, 24kHz)
            processed_audio = self._preprocess_audio(audio_data)
            self.audio_queue.put(processed_audio, block=False)
            self.last_audio_time = time.time()

        except queue.Full:
            logger.warning("Audio queue is full, dropping chunk")

    def _preprocess_audio(self, audio_data: np.ndarray) -> bytes:
        """Preprocess audio for OpenAI Realtime API"""
        # Ensure audio is mono
        if len(audio_data.shape) > 1:
            audio_data = np.mean(audio_data, axis=1)

        # Resample to 24kHz if needed
        if hasattr(self, 'current_sample_rate') and self.current_sample_rate != Config.SAMPLE_RATE:
            # Simple resampling (for production, use scipy.signal.resample)
            resample_ratio = Config.SAMPLE_RATE / self.current_sample_rate
            new_length = int(len(audio_data) * resample_ratio)
            audio_data = signal.resample(audio_data, new_length)

        # Normalize audio
        if np.max(np.abs(audio_data)) > 0:
            audio_data = audio_data / np.max(np.abs(audio_data)) * 0.8

        # Convert to 16-bit PCM
        audio_int16 = (audio_data * 32767).astype(np.int16)

        return audio_int16.tobytes()

    def _detect_silence(self, audio_data: np.ndarray) -> bool:
        """Detect if audio chunk contains silence"""
        rms = np.sqrt(np.mean(audio_data ** 2))
        return rms < Config.SILENCE_THRESHOLD

    def _process_audio_loop(self):
        """Main audio processing loop"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            loop.run_until_complete(self._async_process_audio())
        except Exception as e:
            logger.error(f"Error in audio processing loop: {e}")
        finally:
            loop.close()

    async def _async_process_audio(self):
        """Async audio processing with OpenAI"""
        # Connect to OpenAI
        await self.openai_client.connect()

        audio_buffer = []
        last_commit_time = time.time()

        while self.is_processing:
            try:
                # Get audio chunk with timeout
                try:
                    audio_chunk = self.audio_queue.get(timeout=0.1)
                    audio_buffer.append(audio_chunk)

                    # Send audio to OpenAI
                    await self.openai_client.send_audio(audio_chunk)

                except queue.Empty:
                    # Check for silence timeout
                    current_time = time.time()
                    if (self.last_audio_time and
                        current_time - self.last_audio_time > Config.SILENCE_DURATION and
                        audio_buffer):

                        # Commit audio buffer after silence
                        await self.openai_client.commit_audio()
                        audio_buffer.clear()
                        last_commit_time = current_time
                        logger.info("Audio committed due to silence")

                    continue

                # Commit audio periodically or when buffer is large
                current_time = time.time()
                if (current_time - last_commit_time > 5.0 or  # Every 5 seconds
                    len(audio_buffer) > 50):  # Or when buffer is large

                    await self.openai_client.commit_audio()
                    audio_buffer.clear()
                    last_commit_time = current_time
                    logger.info("Audio committed (periodic)")

            except Exception as e:
                logger.error(f"Error processing audio chunk: {e}")
                await asyncio.sleep(0.1)

    def _handle_audio_response(self, audio_data: bytes):
        """Handle audio response from OpenAI"""
        try:
            # Add to playback buffer if enabled
            if Config.ENABLE_AUDIO_PLAYBACK:
                self.audio_output_buffer.put(audio_data, block=False)

            # Also call the original callback for compatibility
            if self.on_audio_output:
                self.on_audio_output(audio_data)
        except Exception as e:
            logger.error(f"Error handling audio response: {e}")

    def _handle_conversation_event(self, event_type: str, data: dict):
        """Handle conversation events from OpenAI client"""
        self.conversation_events.append({"type": event_type, "data": data, "timestamp": time.time()})

        # Update internal state based on events
        if event_type == "user_speaking":
            self.is_user_speaking = True
            self.conversation_state = "speaking"
        elif event_type == "user_stopped":
            self.is_user_speaking = False
            self.conversation_state = "processing"
        elif event_type == "ai_speaking":
            self.is_ai_speaking = True
            self.conversation_state = "ai_responding"
        elif event_type in ["ai_finished", "response_complete"]:
            self.is_ai_speaking = False
            self.conversation_state = "listening"
        elif event_type == "interrupted":
            self.is_ai_speaking = False
            self.conversation_state = "listening"

        # Notify external listeners
        self._notify_conversation_event(event_type, data)
        logger.info(f"Conversation event: {event_type} - {data.get('message', '')}")

    def _notify_conversation_event(self, event_type: str, data: dict):
        """Notify external listeners about conversation events"""
        if self.on_conversation_event:
            self.on_conversation_event(event_type, data)

    def _audio_playback_loop(self):
        """Audio playback loop for real-time audio output"""
        logger.info("Audio playback loop started")

        try:
            import sounddevice as sd
            audio_playback_available = True
            logger.info("Using sounddevice for audio playback")
        except ImportError:
            audio_playback_available = False
            logger.warning("sounddevice not available, audio will not play")

        while self.is_processing:
            try:
                # Get audio data from buffer
                audio_data = self.audio_output_buffer.get(timeout=0.1)

                if audio_playback_available:
                    try:
                        # Convert bytes to numpy array for playback
                        audio_array = np.frombuffer(audio_data, dtype=np.int16)

                        # Convert to float32 for sounddevice
                        audio_float = audio_array.astype(np.float32) / 32768.0

                        # Play audio chunk
                        sd.play(audio_float, samplerate=Config.SAMPLE_RATE, blocking=False)
                        logger.debug(f"Playing audio chunk: {len(audio_data)} bytes")

                    except Exception as e:
                        logger.error(f"Error playing audio with sounddevice: {e}")
                else:
                    # Fallback: just log that we received audio
                    logger.debug(f"Received audio chunk: {len(audio_data)} bytes (no playback)")

            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Error in audio playback: {e}")

    def get_conversation_state(self) -> dict:
        """Get current conversation state"""
        return {
            "state": self.conversation_state,
            "is_user_speaking": self.is_user_speaking,
            "is_ai_speaking": self.is_ai_speaking,
            "recent_events": self.conversation_events[-5:] if self.conversation_events else []
        }

    def set_sample_rate(self, sample_rate: int):
        """Set the current sample rate for audio preprocessing"""
        self.current_sample_rate = sample_rate
